<template>
  <div class="table-container-wrapper">
    <!-- 表格容器 -->
    <div class="table-wrapper">
      <div class="table-container" ref="tableContainer" :style="tableContainerStyle">
        <div class="table-scroll-container">
          <table ref="editableTable" :style="tableStyle" @contextmenu="handleContextMenu">
          <!-- 表头第一行 - 不可编辑 -->
          <tr>
            <td class="header-cell" rowspan="2" :title="'检查工序名称'">
              检查工序名称
            </td>
            <td class="header-cell" rowspan="2" :title="'检查项目及技术条件'">
              检查项目及技术条件
            </td>
            <td class="header-cell" rowspan="2" :title="'实际检查结果'">
              实际检查结果
            </td>
            <td class="header-cell" colspan="2" :title="'完工'">
              完工
            </td>
            <td class="header-cell" rowspan="2" :title="'操作员'">
              操作员
            </td>
            <td class="header-cell" rowspan="2" :title="'班组长'">
              班组长
            </td>
            <td class="header-cell" rowspan="2" :title="'检验员'">
              检验员
            </td>
          </tr>
          <!-- 表头第二行 - 不可编辑 -->
          <tr>
            <td class="header-cell" :title="'月'">
              月
            </td>
            <td class="header-cell" :title="'日'">
              日
            </td>
          </tr>
          <!-- 数据行 - 可编辑 -->
          <tr v-for="(row, rowIndex) in dataRows" :key="rowIndex">
            <td
              v-for="(cell, cellIndex) in row"
              :key="cellIndex"
              class="editable-cell"
              :class="{ 'has-math': cell.hasMath }"
              :title="cell.content"
              @click="handleCellClick(rowIndex, cellIndex, $event)"
              @dblclick="handleCellDoubleClick(rowIndex, cellIndex, $event)"
              @mousedown="handleCellMouseDown(rowIndex, cellIndex, $event)"
              @blur="finishEdit(rowIndex, cellIndex)"
              @keydown.enter="handleEnterKey(rowIndex, cellIndex, $event)"
              @keydown.esc="cancelEdit(rowIndex, cellIndex)"
              :contenteditable="cell.isEditing"
              @input="updateCellContent(rowIndex, cellIndex, $event)"
              @compositionstart="handleCompositionStart(rowIndex, cellIndex)"
              @compositionend="handleCompositionEnd(rowIndex, cellIndex, $event)"
            >
              <template v-if="!cell.isEditing">
                <span v-html="formatCellContent(cell.content)"></span>
              </template>
            </td>
          </tr>
        </table>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div
        class="context-menu-item"
        @click="handleRowHeightClick"
        @mousedown.stop
      >
        调整此行高度
      </div>
      <div
        class="context-menu-item"
        @click="handleColumnWidthClick"
        @mousedown.stop
      >
        调整此列宽度
      </div>
      <div class="context-menu-divider"></div>
      <div
        v-if="isDataRow"
        class="context-menu-item delete-item"
        @click="handleDeleteRowClick"
        @mousedown.stop
      >
        删除此行
      </div>
    </div>

    <!-- 行高调整对话框 -->
    <div v-if="rowHeightDialogVisible" class="dialog-overlay" @click="closeRowHeightDialog">
      <div class="dialog" @click.stop>
        <h3>调整行高度</h3>
        <div class="dialog-content">
          <label>当前行高度: {{ currentRowHeight }}px</label>
          <input
            type="number"
            v-model="newRowHeight"
            :min="minCellHeight"
            placeholder="输入新的行高度(px)"
            @keydown.enter="applyRowHeight"
            @keydown.esc="closeRowHeightDialog"
            ref="rowHeightInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeRowHeightDialog" class="btn-cancel">取消</button>
          <button @click="applyRowHeight" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 列宽调整对话框 -->
    <div v-if="columnWidthDialogVisible" class="dialog-overlay" @click="closeColumnWidthDialog">
      <div class="dialog" @click.stop>
        <h3>调整列宽度</h3>
        <div class="dialog-content">
          <label>当前列宽度: {{ currentColumnWidth }}px</label>
          <input
            type="number"
            v-model="newColumnWidth"
            :min="minCellWidth"
            placeholder="输入新的列宽度(px)"
            @keydown.enter="applyColumnWidth"
            @keydown.esc="closeColumnWidthDialog"
            ref="columnWidthInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeColumnWidthDialog" class="btn-cancel">取消</button>
          <button @click="applyColumnWidth" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TableContainer',
  props: {
    // 表格尺寸
    tableWidth: {
      type: String,
      default: '1600px'
    },
    tableHeight: {
      type: String,
      default: '300px'
    },
    // 表格数据
    dataRows: {
      type: Array,
      default: () => [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ]
    },
    // 最小尺寸限制
    minCellWidth: {
      type: Number,
      default: 20
    },
    minCellHeight: {
      type: Number,
      default: 20
    }
  },
  data() {
    return {
      // 右键菜单状态
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextCell: null,
      isDataRow: false,
      currentRowIndex: -1,
      currentColumnIndex: -1,

      // 行高调整对话框
      rowHeightDialogVisible: false,
      currentRowHeight: 0,
      newRowHeight: 0,

      // 列宽调整对话框
      columnWidthDialogVisible: false,
      currentColumnWidth: 0,
      newColumnWidth: 0,

      // MathJax相关
      mathJaxReady: false,

      // 中文输入法状态
      isComposing: false,

      // 点击状态跟踪
      clickTimer: null,
      clickCount: 0
    }
  },
  computed: {
    tableContainerStyle() {
      const style = {
        width: this.tableWidth
      }

      if (this.tableHeight !== 'auto') {
        style.height = this.tableHeight
        style.maxHeight = this.tableHeight
      }

      return style
    },
    tableStyle() {
      return {
        width: '100%',
        height: 'auto'
      }
    }
  },
  mounted() {
    this.setupEventListeners()
    this.initializeMathJax()
  },
  beforeDestroy() {
    this.removeEventListeners()

    if (this.clickTimer) {
      clearTimeout(this.clickTimer)
      this.clickTimer = null
    }
  },
  methods: {
    // 设置事件监听器
    setupEventListeners() {
      document.addEventListener('mousedown', this.handleDocumentClick)
    },

    // 移除事件监听器
    removeEventListeners() {
      document.removeEventListener('mousedown', this.handleDocumentClick)
    },

    // 处理右键菜单
    handleContextMenu(e) {
      e.preventDefault()

      const cell = e.target.closest('td')
      if (!cell) return

      this.currentContextCell = cell
      this.contextMenuX = e.pageX
      this.contextMenuY = e.pageY
      this.contextMenuVisible = true

      // 判断是否为数据行（非表头）
      this.isDataRow = cell.classList.contains('editable-cell')

      // 计算当前单元格的行列索引
      this.calculateCellPosition(cell)

      // 获取当前行高和列宽
      this.getCurrentRowHeight()
      this.getCurrentColumnWidth()

      // 调整菜单位置避免超出屏幕
      this.$nextTick(() => {
        this.adjustContextMenuPosition()
      })
    },

    // 计算单元格位置
    calculateCellPosition(cell) {
      const table = this.$refs.editableTable
      if (!table) return

      const rows = Array.from(table.rows)
      const rowIndex = rows.findIndex(row => Array.from(row.cells).includes(cell))
      this.currentRowIndex = rowIndex

      if (rowIndex >= 0) {
        let columnIndex = 0
        const row = rows[rowIndex]
        const cells = Array.from(row.cells)
        const cellIndex = cells.indexOf(cell)

        for (let i = 0; i < cellIndex; i++) {
          const colspan = parseInt(cells[i].getAttribute('colspan') || '1')
          columnIndex += colspan
        }

        this.currentColumnIndex = columnIndex
      } else {
        this.currentColumnIndex = 0
      }
    },

    // 获取当前行高度
    getCurrentRowHeight() {
      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        if (table && table.rows[this.currentRowIndex]) {
          const row = table.rows[this.currentRowIndex]
          const rect = row.getBoundingClientRect()
          this.currentRowHeight = Math.round(rect.height)
        } else {
          this.currentRowHeight = 50
        }
      } else {
        this.currentRowHeight = 50
      }
    },

    // 获取当前列宽度
    getCurrentColumnWidth() {
      if (this.currentContextCell) {
        const rect = this.currentContextCell.getBoundingClientRect()
        this.currentColumnWidth = Math.round(rect.width)
      } else {
        this.currentColumnWidth = 120
      }
    },

    // 调整右键菜单位置
    adjustContextMenuPosition() {
      const menu = document.querySelector('.context-menu')
      if (!menu) return

      const menuRect = menu.getBoundingClientRect()
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      if (this.contextMenuX + menuRect.width > windowWidth) {
        this.contextMenuX = windowWidth - menuRect.width - 10
      }

      if (this.contextMenuY + menuRect.height > windowHeight) {
        this.contextMenuY = windowHeight - menuRect.height - 10
      }
    },

    // 处理文档点击（关闭菜单）
    handleDocumentClick(e) {
      if (!this.contextMenuVisible) {
        return
      }

      if (e.target.closest('.context-menu')) {
        return
      }

      this.contextMenuVisible = false
    },

    // 处理行高点击
    handleRowHeightClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showRowHeightDialog()
    },

    // 处理列宽点击
    handleColumnWidthClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showColumnWidthDialog()
    },

    // 处理删除行点击
    handleDeleteRowClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.deleteCurrentRow()
    },

    // 显示行高调整对话框
    showRowHeightDialog() {
      this.contextMenuVisible = false
      this.newRowHeight = this.currentRowHeight || 50
      this.rowHeightDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.rowHeightInput) {
          this.$refs.rowHeightInput.focus()
          this.$refs.rowHeightInput.select()
        }
      })
    },

    // 显示列宽调整对话框
    showColumnWidthDialog() {
      this.contextMenuVisible = false
      this.newColumnWidth = this.currentColumnWidth || 120
      this.columnWidthDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.columnWidthInput) {
          this.$refs.columnWidthInput.focus()
          this.$refs.columnWidthInput.select()
        }
      })
    },

    // 关闭行高调整对话框
    closeRowHeightDialog() {
      this.rowHeightDialogVisible = false
    },

    // 关闭列宽调整对话框
    closeColumnWidthDialog() {
      this.columnWidthDialogVisible = false
    },

    // 删除当前行
    deleteCurrentRow() {
      if (this.isDataRow && this.currentRowIndex >= 2) {
        const dataRowIndex = this.currentRowIndex - 2

        if (dataRowIndex >= 0 && dataRowIndex < this.dataRows.length) {
          if (confirm('确定要删除这一行吗？此操作不可撤销。')) {
            this.$emit('delete-row', dataRowIndex)
            this.contextMenuVisible = false
          }
        }
      }
    },

    // 应用行高调整
    applyRowHeight() {
      const height = Math.max(this.minCellHeight, parseInt(this.newRowHeight) || this.minCellHeight)

      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        const targetRow = table.rows[this.currentRowIndex]

        const cells = Array.from(targetRow.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')

          if (rowspan > 1) {
            for (let i = 0; i < rowspan; i++) {
              const rowIndex = this.currentRowIndex + i
              if (rowIndex < table.rows.length) {
                this.setRowHeight(table.rows[rowIndex], height / rowspan)
              }
            }
          } else {
            cell.style.height = `${height}px`
            cell.style.minHeight = `${height}px`
          }
        })

        this.adjustCrossRowMergedCells(height)
      }

      this.closeRowHeightDialog()
      this.$emit('table-updated')
    },

    // 设置行高度
    setRowHeight(row, height) {
      const cells = Array.from(row.cells)
      cells.forEach(cell => {
        cell.style.height = `${height}px`
        cell.style.minHeight = `${height}px`
      })
    },

    // 调整跨行合并单元格
    adjustCrossRowMergedCells(newHeight) {
      const table = this.$refs.editableTable
      const rows = Array.from(table.rows)

      for (let i = 0; i < this.currentRowIndex; i++) {
        const row = rows[i]
        const cells = Array.from(row.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')
          if (rowspan > 1 && i + rowspan > this.currentRowIndex) {
            const totalHeight = newHeight * (rowspan - (this.currentRowIndex - i))
            cell.style.height = `${totalHeight}px`
            cell.style.minHeight = `${totalHeight}px`
          }
        })
      }
    },

    // 应用列宽调整
    applyColumnWidth() {
      const newWidth = Math.max(this.minCellWidth, parseInt(this.newColumnWidth) || this.minCellWidth)

      if (this.currentColumnIndex >= 0) {
        const table = this.$refs.editableTable
        const rows = Array.from(table.rows)

        let originalWidth = 0
        const firstRowCell = this.getCellAtColumn(rows[0], this.currentColumnIndex)
        if (firstRowCell) {
          originalWidth = firstRowCell.getBoundingClientRect().width
        }

        const widthDelta = newWidth - originalWidth

        rows.forEach((row) => {
          const cell = this.getCellAtColumn(row, this.currentColumnIndex)
          if (cell) {
            const colspan = parseInt(cell.getAttribute('colspan') || '1')

            if (colspan > 1) {
              const currentCellWidth = cell.getBoundingClientRect().width
              const newCellWidth = currentCellWidth + widthDelta
              cell.style.width = `${newCellWidth}px`
              cell.style.minWidth = `${newCellWidth}px`
            } else {
              cell.style.width = `${newWidth}px`
              cell.style.minWidth = `${newWidth}px`
            }
          }
        })

        const currentTableWidth = table.getBoundingClientRect().width
        const newTableWidth = currentTableWidth + widthDelta
        table.style.width = `${newTableWidth}px`

        this.$emit('table-width-changed', `${newTableWidth}px`)
      }

      this.closeColumnWidthDialog()
      this.$emit('table-updated')
    },

    // 获取指定列的单元格
    getCellAtColumn(row, targetColumnIndex) {
      const cells = Array.from(row.cells)
      let currentColumnIndex = 0

      for (let i = 0; i < cells.length; i++) {
        const cell = cells[i]
        const colspan = parseInt(cell.getAttribute('colspan') || '1')

        if (currentColumnIndex <= targetColumnIndex && currentColumnIndex + colspan > targetColumnIndex) {
          return cell
        }

        currentColumnIndex += colspan
      }

      return null
    },

    // 处理单元格鼠标按下
    handleCellMouseDown(rowIndex, cellIndex, event) {
      if (!this.dataRows[rowIndex]) {
        this.$emit('ensure-row', rowIndex)
      }

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell && cell.isEditing) {
        event.stopPropagation()
      }
    },

    // 处理单元格点击
    handleCellClick(rowIndex, cellIndex, event) {
      if (!this.dataRows[rowIndex]) {
        this.$emit('ensure-row', rowIndex)
      }

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        if (!cell.isEditing) {
          this.startEdit(rowIndex, cellIndex, true)
        } else {
          this.clickCount++

          if (this.clickTimer) {
            clearTimeout(this.clickTimer)
          }

          this.clickTimer = setTimeout(() => {
            if (this.clickCount === 1) {
              // 单击：保持光标位置
            }
            this.clickCount = 0
          }, 200)
        }
      }
    },

    // 处理单元格双击
    handleCellDoubleClick(rowIndex, cellIndex, event) {
      if (this.clickTimer) {
        clearTimeout(this.clickTimer)
        this.clickTimer = null
      }
      this.clickCount = 0

      const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
      if (cell && cell.isEditing) {
        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell && cell.content) {
          setTimeout(() => {
            try {
              const range = document.createRange()
              const selection = window.getSelection()
              range.selectNodeContents(tableCell)
              selection.removeAllRanges()
              selection.addRange(range)
            } catch (error) {
              console.warn('双击选中文本失败:', error)
            }
          }, 10)
        }
      }
    },

    // 开始编辑单元格
    startEdit(rowIndex, cellIndex, selectAll = true) {
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.originalContent = cell.content
        this.$emit('start-edit', { rowIndex, cellIndex, cell })

        this.$nextTick(() => {
          const tableCell = this.getCellElement(rowIndex, cellIndex)
          if (tableCell) {
            tableCell.textContent = cell.content
            tableCell.focus()

            if (selectAll && cell.content) {
              setTimeout(() => {
                try {
                  const range = document.createRange()
                  const selection = window.getSelection()
                  range.selectNodeContents(tableCell)
                  selection.removeAllRanges()
                  selection.addRange(range)
                } catch (error) {
                  console.warn('选中文本失败:', error)
                }
              }, 10)
            } else if (!cell.content) {
              const range = document.createRange()
              const selection = window.getSelection()
              range.setStart(tableCell, 0)
              range.setEnd(tableCell, 0)
              selection.removeAllRanges()
              selection.addRange(range)
            }
          }
        })
      }
    },

    // 获取单元格DOM元素
    getCellElement(rowIndex, cellIndex) {
      const table = this.$refs.editableTable
      if (table) {
        const targetRow = table.rows[rowIndex + 2]
        if (targetRow && targetRow.cells[cellIndex]) {
          return targetRow.cells[cellIndex]
        }
      }
      return null
    },

    // 处理Enter键
    handleEnterKey(rowIndex, cellIndex, event) {
      console.log('Enter键被按下:', rowIndex, cellIndex, 'Shift键:', event.shiftKey, 'Ctrl键:', event.ctrlKey, '中文输入状态:', this.isComposing)

      // 如果正在中文输入，不处理Enter键
      if (this.isComposing) {
        console.log('中文输入中，忽略Enter键')
        return
      }

      // 如果按住Shift+Enter或Ctrl+Enter，插入换行符
      if (event.shiftKey || event.ctrlKey) {
        event.preventDefault()
        event.stopPropagation()

        console.log('准备插入换行符')

        // 获取当前光标位置并插入换行符
        const selection = window.getSelection()
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0)

          // 创建换行符
          const lineBreak = document.createTextNode('\n')

          // 删除选中内容（如果有）
          range.deleteContents()

          // 插入换行符
          range.insertNode(lineBreak)

          // 将光标移动到换行符后面
          range.setStartAfter(lineBreak)
          range.setEndAfter(lineBreak)
          range.collapse(true)

          // 更新选择
          selection.removeAllRanges()
          selection.addRange(range)

          console.log('换行符插入成功，光标位置已更新')

          // 手动触发input事件，确保内容被保存
          const cell = this.dataRows[rowIndex][cellIndex]
          const tableCell = this.getCellElement(rowIndex, cellIndex)
          if (tableCell && cell) {
            const newContent = tableCell.textContent || ''
            cell.content = newContent
            console.log('单元格内容已更新:', newContent)
          }
        } else {
          console.warn('没有找到有效的选择范围')
        }
      } else {
        // 普通Enter键，完成编辑
        console.log('普通Enter键，完成编辑')
        this.finishEdit(rowIndex, cellIndex)
      }
    },

    // 完成编辑
    finishEdit(rowIndex, cellIndex) {
      if (this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]) {
        const cell = this.dataRows[rowIndex][cellIndex]

        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell) {
          const newContent = tableCell.textContent || ''
          cell.content = newContent
          cell.hasMath = this.containsMath(cell.content)

          this.$emit('finish-edit', { rowIndex, cellIndex, cell, newContent })

          if (cell.hasMath) {
            this.$nextTick(async () => {
              try {
                await this.renderMathJax(tableCell)
              } catch (error) {
                console.error('MathJax渲染失败:', error)
              }
            })
          }
        } else {
          this.$emit('finish-edit', { rowIndex, cellIndex, cell, newContent: cell.content })
        }
      }
    },

    // 取消编辑
    cancelEdit(rowIndex, cellIndex) {
      if (this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]) {
        const cell = this.dataRows[rowIndex][cellIndex]
        cell.content = cell.originalContent

        this.$emit('cancel-edit', { rowIndex, cellIndex, cell })

        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell) {
          tableCell.textContent = cell.content
        }
      }
    },

    // 更新单元格内容
    updateCellContent(rowIndex, cellIndex, event) {
      if (this.isComposing) {
        console.log('中文输入中，跳过内容更新')
        return
      }

      // 实时更新单元格内容
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell && event.target) {
        const newContent = event.target.textContent || ''
        cell.content = newContent
        console.log('实时更新单元格内容:', newContent)
      }
    },

    // 处理中文输入开始
    handleCompositionStart(rowIndex, cellIndex) {
      console.log('中文输入开始:', rowIndex, cellIndex)
      this.isComposing = true
    },

    // 处理中文输入结束
    handleCompositionEnd(rowIndex, cellIndex, event) {
      console.log('中文输入结束:', rowIndex, cellIndex)
      this.isComposing = false

      // 输入法结束后，更新单元格内容
      this.$nextTick(() => {
        const cell = this.dataRows[rowIndex][cellIndex]
        const tableCell = this.getCellElement(rowIndex, cellIndex)
        if (tableCell && cell) {
          const newContent = tableCell.textContent || ''
          cell.content = newContent
          console.log('输入法结束后更新内容:', newContent)
        }
      })
    },

    // 格式化单元格内容
    formatCellContent(content) {
      if (!content) return ''

      const escaped = content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')

      return escaped.replace(/\n/g, '<br>')
    },

    // 检测内容是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // 初始化MathJax
    async initializeMathJax() {
      try {
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']]
          },
          chtml: {
            fontURL: '/fonts/mathjax'
          }
        }

        const mathJaxScript = document.createElement('script')
        mathJaxScript.id = 'MathJax-script'
        mathJaxScript.async = true
        mathJaxScript.src = '/js/mathjax/tex-mml-chtml-mathjax-newcm.js'

        mathJaxScript.onload = () => {
          this.mathJaxReady = true
        }

        mathJaxScript.onerror = () => {
          this.mathJaxReady = false
        }

        document.head.appendChild(mathJaxScript)

      } catch (error) {
        console.error('MathJax初始化失败:', error)
        this.mathJaxReady = false
      }
    },

    // 渲染MathJax公式
    async renderMathJax(element) {
      try {
        if (!this.mathJaxReady || !window.MathJax) {
          let attempts = 0
          while ((!window.MathJax || !this.mathJaxReady) && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100))
            attempts++
          }
        }

        if (window.MathJax && window.MathJax.typesetPromise) {
          await window.MathJax.typesetPromise([element])
        }
      } catch (error) {
        console.error('MathJax渲染错误:', error)
      }
    },

    // 应用统一尺寸设置
    applyUniformSize(width, height) {
      const table = this.$refs.editableTable
      if (table) {
        const cells = table.querySelectorAll('td')

        cells.forEach((cell) => {
          cell.style.width = `${width}px`
          cell.style.minWidth = `${width}px`
          cell.style.height = `${height}px`
          cell.style.minHeight = `${height}px`
        })

        this.$emit('table-updated')
      }
    },

    // 获取表格引用（供父组件使用）
    getTableRef() {
      return this.$refs.editableTable
    },

    // 获取表格容器引用（供父组件使用）
    getTableContainerRef() {
      return this.$refs.tableContainer
    }
  }
}
</script>

<style scoped>
/* 表格容器样式 */
.table-container-wrapper {
  position: relative;
}

.table-wrapper {
  width: 100%;
  overflow: auto;
}

.table-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: auto;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-scroll-container {
  overflow: auto;
  max-height: 100%;
}

table {
  border-collapse: collapse;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
}

/* 表头样式 */
.header-cell {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #ddd;
  padding: 12px 8px;
  text-align: center;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  user-select: none;
  cursor: default;
  min-width: 120px;
  min-height: 50px;
}

/* 可编辑单元格样式 */
.editable-cell {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  cursor: text;
  transition: all 0.2s ease;
  min-width: 120px;
  min-height: 50px;
  white-space: pre-wrap;
  word-wrap: break-word;
  vertical-align: top;
}

.editable-cell:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

.editable-cell[contenteditable="true"] {
  background-color: #fff3cd;
  border-color: #ffc107;
  outline: 2px solid rgba(255, 193, 7, 0.3);
}

.editable-cell.has-math {
  background-color: #e8f5e8;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item.delete-item {
  color: #dc3545;
}

.context-menu-item.delete-item:hover {
  background-color: #f8d7da;
}

.context-menu-divider {
  height: 1px;
  background-color: #eee;
  margin: 4px 0;
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-content label {
  display: block;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.dialog-content input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel, .btn-confirm {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-confirm {
  background: #007bff;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
}

.btn-confirm:hover {
  background: #0056b3;
}
</style>
