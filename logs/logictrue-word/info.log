09:41:27.816 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 97151 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:41:27.819 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:41:28.869 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:41:28.870 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:41:28.870 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:41:28.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:41:29.611 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:41:29.907 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:41:30.634 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:41:30.650 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 3.33 seconds (JVM running for 4.748)
09:41:52.635 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:42:28.212 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:42:28.212 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:42:28.431 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:42:28.481 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:42:28.482 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:42:28.483 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:42:28.519 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:42:28.519 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:42:28.548 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:42:28.550 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:42:28.550 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:40.707 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:42.733 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:42:42.734 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:42:42.736 [http-nio-9550-exec-7] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:42:43.139 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:44.235 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:45.841 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:47.141 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:48.037 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:42:48.938 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:42:49.017 [http-nio-9550-exec-7] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2997 bytes
09:42:49.024 [http-nio-9550-exec-7] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_094249.docx, 大小: 2997 bytes
09:45:38.171 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:45:38.172 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:45:38.173 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:45:38.174 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:45:38.175 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:45:38.176 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:45:38.178 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:45:38.178 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:45:38.185 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:45:38.186 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:45:38.186 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:45:38.187 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:45:38.187 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:45:38.187 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:48:07.745 [http-nio-9550-exec-8] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:48:10.081 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.082 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:48:10.084 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:48:10.091 [http-nio-9550-exec-8] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 2997 bytes
09:48:10.094 [http-nio-9550-exec-8] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_094810.docx, 大小: 2997 bytes
09:49:04.679 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,350] - HikariPool-1 - Shutdown initiated...
09:49:04.682 [SpringApplicationShutdownHook] INFO  c.z.h.HikariDataSource - [close,352] - HikariPool-1 - Shutdown completed.
09:49:08.782 [main] INFO  c.l.LogicTrueWordApplication - [logStarting,55] - Starting LogicTrueWordApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 107509 (/home/<USER>/nl-mes/word-nl/logictrue-word/target/classes started by tao in /home/<USER>/nl-mes/word-nl)
09:49:08.784 [main] INFO  c.l.LogicTrueWordApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
09:49:09.617 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9550"]
09:49:09.618 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:49:09.618 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
09:49:09.661 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:49:10.170 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
09:49:10.434 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
09:49:11.033 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9550"]
09:49:11.049 [main] INFO  c.l.LogicTrueWordApplication - [logStarted,61] - Started LogicTrueWordApplication in 2.653 seconds (JVM running for 3.286)
09:49:13.697 [http-nio-9550-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:49:13.796 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:49:13.797 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:49:14.118 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:49:14.159 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:49:14.160 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:49:14.161 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:49:14.195 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:49:14.195 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:49:14.221 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:49:14.223 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:49:14.224 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:14.224 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:14.225 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:49:14.225 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:49:18.052 [http-nio-9550-exec-1] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:49:18.343 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.344 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.345 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.346 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.346 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:49:18.354 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:49:18.407 [http-nio-9550-exec-1] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3001 bytes
09:49:18.416 [http-nio-9550-exec-1] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_094918.docx, 大小: 3001 bytes
09:50:29.070 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,38] - 接收到表格导出请求，标题: 检验记录表
09:50:29.070 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,37] - 开始导出Word文档，表格标题: 检验记录表
09:50:29.072 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [setPageOrientation,569] - 已设置文档为横向纸张
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,83] - 接收到表头数据，共2行
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第1行表头，共8列
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列0: content='检查工序名称', rowspan=2, colspan=1
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列1: content='检查项目及技术条件', rowspan=2, colspan=1
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列2: content='实际检查结果', rowspan=2, colspan=1
09:50:29.073 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='完工', rowspan=1, colspan=2
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列5: content='操作员', rowspan=2, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列6: content='班组长', rowspan=2, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列7: content='检验员', rowspan=2, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,86] - 第2行表头，共8列
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列3: content='月', rowspan=1, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,90] -   列4: content='日', rowspan=1, colspan=1
09:50:29.074 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,110] - 创建表格: 3行 x 8列
09:50:29.078 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,155] - 开始处理表头，共2行
09:50:29.078 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第1行表头，共8列
09:50:29.085 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,161] - 处理第2行表头，共8列
09:50:29.086 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processHeadersWithMerge,170] - 表头处理完成，共处理2行
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,337] - 处理混合内容，mathMLMap大小: 1
09:50:29.087 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [insertMixedContent,434] - 插入混合内容，内容: 你好
__MATH_FORMULA_0__
海
不错

嘿嘿
而已
111
432
, 公式映射: {__MATH_FORMULA_0__=<math xmlns="http://www.w3.org/1998/Math/MathML"><msqrt><mi>x</mi></msqrt></math>}
09:50:29.088 [http-nio-9550-exec-2] INFO  c.l.w.s.MathMLToOMMLService - [convertOMML,38] - 转换OMML，MML2OMML.XSL路径: /home/<USER>/nl-mes/word-nl/logictrue-word/target/classes/MML2OMML.XSL
09:50:29.186 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.187 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [processDataRow,351] - 处理普通文本内容
09:50:29.189 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [createTable,144] - 表格创建完成
09:50:29.195 [http-nio-9550-exec-2] INFO  c.l.w.s.WordExportService - [exportTableToWord,62] - Word文档导出完成，文件大小: 3045 bytes
09:50:29.198 [http-nio-9550-exec-2] INFO  c.l.w.c.WordExportController - [exportTable,52] - 表格导出成功，文件名: %E6%A3%80%E9%AA%8C%E8%AE%B0%E5%BD%95%E8%A1%A8_20250820_095029.docx, 大小: 3045 bytes
